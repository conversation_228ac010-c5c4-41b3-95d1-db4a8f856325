# Google Sheets Custom Menu Extension

Chrome расширение для добавления кастомного пункта в контекстное меню Google Таблиц.

## Функциональность

Расширение добавляет пункт "Показать значение ячейки" в контекстное меню Google Таблиц, который при клике показывает alert с содержимым выбранной ячейки.

## Установка

### Способ 1: Загрузка в режиме разработчика

1. Откройте Chrome и перейдите в `chrome://extensions/`
2. Включите "Режим разработчика" в правом верхнем углу
3. Наж<PERSON>ите "Загрузить распакованное расширение"
4. Выберите папку с файлами расширения
5. Расширение будет установлено и активировано

### Способ 2: Создание .crx файла (опционально)

1. В `chrome://extensions/` нажмите "Упаковать расширение"
2. Выберите папку с расширением
3. Получите .crx файл для распространения

## Использование

1. Откройте Google Таблицы (https://docs.google.com/spreadsheets)
2. Выберите любую ячейку с данными
3. Нажмите правой кнопкой мыши для вызова контекстного меню
4. В меню найдите пункт "Показать значение ячейки"
5. Кликните по нему - появится alert с содержимым ячейки

## Структура файлов

```
├── manifest.json       # Конфигурация расширения
├── content.js         # Основной скрипт для внедрения в страницы
├── background.js      # Фоновый скрипт
├── popup.html         # HTML для popup окна
├── popup.js          # Скрипт для popup окна
├── icons/            # Иконки расширения
│   └── icon.svg      # SVG иконка
└── README.md         # Этот файл
```

## Технические детали

- **Manifest Version**: 3 (современный стандарт)
- **Permissions**: `activeTab` (доступ только к активной вкладке)
- **Content Scripts**: Внедряется только на страницы Google Таблиц
- **Target**: `https://docs.google.com/spreadsheets/*`

## Разработка

### Отладка

1. Откройте DevTools на странице Google Таблиц
2. В консоли будут видны сообщения от расширения
3. Для отладки popup: правый клик на иконке расширения → "Проверить всплывающее окно"

### Модификация

- Основная логика находится в `content.js`
- Для изменения внешнего вида пункта меню редактируйте функцию `createCustomMenuItem()`
- Для изменения логики получения значения ячейки редактируйте функцию `getSelectedCellValue()`

## Возможные улучшения

1. Добавление дополнительных пунктов меню
2. Копирование значения в буфер обмена
3. Форматирование отображения значений
4. Поддержка формул и вычисленных значений
5. Экспорт данных ячейки

## Лицензия

MIT License
