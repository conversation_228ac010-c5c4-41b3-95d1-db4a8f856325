# Инструкции по тестированию расширения

## Подготовка к тестированию

1. **Установите расширение в Chrome:**
   - Откройте `chrome://extensions/`
   - Включите "Режим разработчика"
   - Нажмите "Загрузить распакованное расширение"
   - Выберите папку с файлами расширения

2. **Убедитесь, что расширение активно:**
   - В списке расширений должно появиться "Google Sheets Custom Menu"
   - Статус должен быть "Включено"

## Тестовые сценарии

### Сценарий 1: Базовая функциональность

1. Откройте Google Таблицы: https://docs.google.com/spreadsheets
2. Создайте новую таблицу или откройте существующую
3. Введите текст в любую ячейку (например, "Тестовое значение")
4. Выберите эту ячейку
5. Нажмите правой кнопкой мыши
6. **Ожидаемый результат:** В контекстном меню должен появиться пункт "Показать значение ячейки"
7. Кликните по этому пункту
8. **Ожидаемый результат:** Должен появиться alert с текстом "Значение ячейки: Тестовое значение"

### Сценарий 2: Тестирование с числами

1. Введите число в ячейку (например, 123.45)
2. Выберите ячейку и вызовите контекстное меню
3. Кликните "Показать значение ячейки"
4. **Ожидаемый результат:** Alert должен показать "Значение ячейки: 123.45"

### Сценарий 3: Тестирование с формулами

1. Введите формулу в ячейку (например, =A1+B1)
2. Выберите ячейку и вызовите контекстное меню
3. Кликните "Показать значение ячейки"
4. **Ожидаемый результат:** Alert должен показать вычисленное значение или формулу

### Сценарий 4: Тестирование с пустой ячейкой

1. Выберите пустую ячейку
2. Вызовите контекстное меню
3. Кликните "Показать значение ячейки"
4. **Ожидаемый результат:** Alert должен показать пустое значение или сообщение о пустой ячейке

### Сценарий 5: Тестирование popup окна

1. Кликните на иконку расширения в панели инструментов Chrome
2. **Ожидаемый результат:** Должно открыться popup окно с инструкциями
3. Если вы на странице Google Таблиц, статус должен показывать "✅ Расширение активно на Google Таблицах"
4. Если вы на другой странице, статус должен показывать "⚠️ Откройте Google Таблицы для использования"

## Отладка

### Проверка консоли

1. Откройте DevTools (F12) на странице Google Таблиц
2. Перейдите на вкладку "Console"
3. **Ожидаемые сообщения:**
   - "Google Sheets Custom Menu Extension загружено"
   - "Обнаружено контекстное меню" (при вызове контекстного меню)
   - "Кастомный пункт меню добавлен" (при добавлении пункта)

### Проверка элементов DOM

1. В DevTools перейдите на вкладку "Elements"
2. Вызовите контекстное меню в таблице
3. Найдите элемент с классом `goog-menu`
4. **Ожидаемый результат:** Внутри должен быть элемент с классом `custom-cell-value-item`

### Проверка ошибок

1. В консоли не должно быть ошибок JavaScript
2. Если есть ошибки, проверьте:
   - Правильность селекторов в `content.js`
   - Совместимость с текущей версией Google Таблиц
   - Права доступа расширения

## Возможные проблемы и решения

### Проблема: Пункт меню не появляется

**Возможные причины:**
- Расширение не загружено или отключено
- Страница не является Google Таблицами
- Изменилась структура DOM Google Таблиц

**Решение:**
- Проверьте статус расширения в `chrome://extensions/`
- Убедитесь, что URL содержит `docs.google.com/spreadsheets`
- Обновите селекторы в `content.js`

### Проблема: Alert не показывает правильное значение

**Возможные причины:**
- Изменились селекторы для получения значения ячейки
- Ячейка содержит сложное форматирование

**Решение:**
- Обновите функцию `getSelectedCellValue()` в `content.js`
- Добавьте дополнительные селекторы для поиска активной ячейки

### Проблема: Расширение не работает после обновления Google Таблиц

**Решение:**
- Google может изменить структуру DOM
- Обновите селекторы в соответствии с новой структурой
- Проверьте консоль на наличие ошибок

## Логи для отладки

Добавьте эти строки в `content.js` для дополнительной отладки:

```javascript
console.log('Текущий URL:', window.location.href);
console.log('Найденные меню:', document.querySelectorAll('.goog-menu'));
console.log('Активная ячейка:', document.querySelector('.cell-input'));
```
