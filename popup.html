<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 300px;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        
        .description {
            font-size: 14px;
            color: #666;
            line-height: 1.4;
        }
        
        .instructions {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-top: 15px;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #333;
            font-size: 16px;
        }
        
        .instructions ol {
            margin: 10px 0;
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 8px;
            font-size: 14px;
            color: #555;
        }
        
        .status {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background-color: #e8f5e8;
            border-radius: 5px;
            color: #2d5a2d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Google Sheets Custom Menu</div>
        <div class="description">
            Расширение для добавления кастомного пункта в контекстное меню Google Таблиц
        </div>
    </div>
    
    <div class="instructions">
        <h3>Как использовать:</h3>
        <ol>
            <li>Откройте Google Таблицы</li>
            <li>Выберите любую ячейку</li>
            <li>Нажмите правой кнопкой мыши</li>
            <li>В контекстном меню найдите пункт "Показать значение ячейки"</li>
            <li>Кликните по нему для отображения alert с содержимым ячейки</li>
        </ol>
    </div>
    
    <div class="status">
        ✅ Расширение активно
    </div>
    
    <script src="popup.js"></script>
</body>
</html>
