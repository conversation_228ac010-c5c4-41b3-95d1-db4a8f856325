// Основной скрипт для внедрения в Google Таблицы
(function() {
    'use strict';
    
    let observer = null;
    let currentSelectedCell = null;
    
    // Функция для получения значения выбранной ячейки
    function getSelectedCellValue() {
        try {
            // Ищем активную ячейку по различным селекторам
            const activeCell = document.querySelector('.cell-input') || 
                              document.querySelector('[data-sheet-cell]') ||
                              document.querySelector('.grid-cell.selected') ||
                              document.querySelector('.cell.selected');
            
            if (activeCell) {
                return activeCell.textContent || activeCell.value || activeCell.innerText || '';
            }
            
            // Альтернативный способ - через строку формул
            const formulaBar = document.querySelector('#t-formula-bar-input') ||
                              document.querySelector('.cell-input') ||
                              document.querySelector('[aria-label*="формула"]');
            
            if (formulaBar) {
                return formulaBar.value || formulaBar.textContent || '';
            }
            
            return 'Не удалось получить значение ячейки';
        } catch (error) {
            console.error('Ошибка при получении значения ячейки:', error);
            return 'Ошибка при получении значения';
        }
    }
    
    // Функция для создания кастомного пункта меню
    function createCustomMenuItem() {
        const menuItem = document.createElement('div');
        menuItem.className = 'goog-menuitem apps-menuitem custom-cell-value-item';
        menuItem.setAttribute('role', 'menuitem');
        menuItem.style.userSelect = 'none';
        menuItem.style.cursor = 'pointer';
        
        menuItem.innerHTML = `
            <div class="goog-menuitem-content">
                <div class="docs-icon goog-inline-block goog-menuitem-icon" aria-hidden="true">
                    <div class="docs-icon-img-container docs-icon-img docs-icon-editors-ia-info" style="background-color: #4285f4; border-radius: 2px;"></div>
                </div>
                <span class="goog-menuitem-label">Показать значение ячейки</span>
            </div>
        `;
        
        // Добавляем обработчик клика
        menuItem.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const cellValue = getSelectedCellValue();
            alert(`Значение ячейки: ${cellValue}`);
            
            // Закрываем меню
            const menu = document.querySelector('.goog-menu');
            if (menu) {
                menu.style.display = 'none';
            }
        });
        
        // Добавляем hover эффекты
        menuItem.addEventListener('mouseenter', function() {
            this.classList.add('goog-menuitem-highlight');
        });
        
        menuItem.addEventListener('mouseleave', function() {
            this.classList.remove('goog-menuitem-highlight');
        });
        
        return menuItem;
    }
    
    // Функция для добавления кастомного пункта в меню
    function addCustomMenuItemToContextMenu(menu) {
        // Проверяем, не добавлен ли уже наш пункт
        if (menu.querySelector('.custom-cell-value-item')) {
            return;
        }
        
        // Создаем разделитель
        const separator = document.createElement('div');
        separator.className = 'goog-menuseparator';
        separator.style.userSelect = 'none';
        separator.setAttribute('aria-disabled', 'true');
        separator.setAttribute('role', 'separator');
        
        // Создаем наш кастомный пункт
        const customItem = createCustomMenuItem();
        
        // Находим подходящее место для вставки (в конец меню)
        menu.appendChild(separator);
        menu.appendChild(customItem);
        
        console.log('Кастомный пункт меню добавлен');
    }
    
    // Функция для отслеживания появления контекстного меню
    function observeContextMenu() {
        if (observer) {
            observer.disconnect();
        }
        
        observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        // Ищем контекстное меню Google Таблиц
                        const menu = node.querySelector && node.querySelector('.goog-menu.shell-menu') ||
                                    (node.classList && node.classList.contains('goog-menu') && node.classList.contains('shell-menu') ? node : null);
                        
                        if (menu && menu.style.display !== 'none') {
                            console.log('Обнаружено контекстное меню');
                            setTimeout(() => addCustomMenuItemToContextMenu(menu), 10);
                        }
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // Инициализация
    function init() {
        console.log('Google Sheets Custom Menu Extension загружено');
        
        // Ждем полной загрузки страницы
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', observeContextMenu);
        } else {
            observeContextMenu();
        }
        
        // Дополнительная инициализация через небольшую задержку
        setTimeout(observeContextMenu, 1000);
    }
    
    // Запускаем инициализацию
    init();
    
})();
