// Фоновый скрипт для Chrome расширения
chrome.runtime.onInstalled.addListener(() => {
    console.log('Google Sheets Custom Menu Extension установлено');
});

// Обработчик сообщений от content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'getCellValue') {
        // Здесь можно добавить дополнительную логику обработки
        sendResponse({success: true});
    }
});

// Обработчик клика по иконке расширения
chrome.action.onClicked.addListener((tab) => {
    // Проверяем, что мы на странице Google Таблиц
    if (tab.url && tab.url.includes('docs.google.com/spreadsheets')) {
        chrome.tabs.sendMessage(tab.id, {action: 'toggleExtension'});
    } else {
        // Открываем Google Таблицы, если мы не на нужной странице
        chrome.tabs.create({url: 'https://docs.google.com/spreadsheets'});
    }
});
