// Скрипт для popup окна расширения
document.addEventListener('DOMContentLoaded', function() {
    // Проверяем, активна ли текущая вкладка с Google Таблицами
    chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
        const currentTab = tabs[0];
        const statusElement = document.querySelector('.status');
        
        if (currentTab.url && currentTab.url.includes('docs.google.com/spreadsheets')) {
            statusElement.innerHTML = '✅ Расширение активно на Google Таблицах';
            statusElement.style.backgroundColor = '#e8f5e8';
            statusElement.style.color = '#2d5a2d';
        } else {
            statusElement.innerHTML = '⚠️ Откройте Google Таблицы для использования';
            statusElement.style.backgroundColor = '#fff3cd';
            statusElement.style.color = '#856404';
        }
    });
    
    // Добавляем обработчик для открытия Google Таблиц
    const statusElement = document.querySelector('.status');
    statusElement.addEventListener('click', function() {
        chrome.tabs.query({active: true, currentWindow: true}, function(tabs) {
            const currentTab = tabs[0];
            if (!currentTab.url || !currentTab.url.includes('docs.google.com/spreadsheets')) {
                chrome.tabs.create({url: 'https://docs.google.com/spreadsheets'});
            }
        });
    });
});
